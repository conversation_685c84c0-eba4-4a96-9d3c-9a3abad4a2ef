#!/usr/bin/env python3
"""
Test script to verify the ReadFiles service integration
"""

import requests
import json

def test_readfiles_service():
    """Test the ReadFiles service with a sample request"""
    
    # ReadFiles service URL
    url = "http://localhost:8003/read-files"
    
    # Sample request data (using a well-known public repository)
    test_data = {
        "repository_url": "https://github.com/octocat/Hello-World",
        "files_to_read": [
            {
                "path": "README",
                "reason": "Main documentation file"
            }
        ]
    }
    
    print("Testing ReadFiles service...")
    print(f"URL: {url}")
    print(f"Request data: {json.dumps(test_data, indent=2)}")
    print("-" * 50)
    
    try:
        # Make the request
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS! ReadFiles service is working")
            print(f"Analysis Method: {result.get('analysis_method', 'Unknown')}")
            print(f"AI Enabled: {result.get('ai_enabled', 'Unknown')}")
            print(f"Files Analyzed: {len(result.get('files_analyzed', []))}")
            
            # Print file analysis results
            for file_info in result.get('files_analyzed', []):
                print(f"\n📄 File: {file_info.get('path', 'Unknown')}")
                print(f"   Reason: {file_info.get('reason', 'Unknown')}")
                print(f"   Has Content: {'Yes' if file_info.get('content') else 'No'}")
                print(f"   Has Analysis: {'Yes' if file_info.get('analysis') else 'No'}")
                print(f"   Error: {file_info.get('error', 'None')}")
                
                if file_info.get('content'):
                    content_preview = file_info['content'][:200] + "..." if len(file_info['content']) > 200 else file_info['content']
                    print(f"   Content Preview: {content_preview}")
            
            if result.get('summary'):
                print(f"\n📋 Summary: {result['summary'][:300]}...")
                
        else:
            print(f"❌ ERROR! Status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR! Could not connect to ReadFiles service.")
        print("Make sure the service is running on http://localhost:8003")
    except Exception as e:
        print(f"❌ ERROR! {str(e)}")

def test_health_endpoint():
    """Test the health endpoint"""
    url = "http://localhost:8003/health"
    
    print("\nTesting health endpoint...")
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint is working")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health endpoint error: {str(e)}")

if __name__ == "__main__":
    test_health_endpoint()
    test_readfiles_service()
