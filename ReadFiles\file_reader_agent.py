# read-files/file_reader_agent.py
import logging
import os
import json
import requests
from typing import List, Dict, Optional
from urllib.parse import urlparse

# --- Optional Imports for AI and Environment Variables ---
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    genai = None

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

logger = logging.getLogger(__name__)

class FileReaderAgent:
    """
    Reads files from a GitHub repository and analyzes their content using Gemini AI.
    """
    
    def __init__(self, repository_url: str, use_ai: bool = True):
        """
        Initializes the FileReaderAgent.

        Args:
            repository_url (str): The GitHub repository URL.
            use_ai (bool): Whether to use AI analysis for file content.
        """
        logger.info("Initializing FileReaderAgent...")
        self.repository_url = repository_url
        self.gemini_model = None
        
        # Parse GitHub URL to get owner and repo
        self.owner, self.repo = self._parse_github_url(repository_url)
        
        logger.info(f"AI usage requested: {use_ai}")
        if use_ai and GEMINI_AVAILABLE:
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                try:
                    genai.configure(api_key=api_key)
                    self.gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
                    logger.info("Gemini model initialized successfully for file reading.")
                except Exception as e:
                    logger.error(f"Failed to initialize Gemini: {e}")
            else:
                logger.warning("GEMINI_API_KEY not found. Using basic file reading.")
        elif use_ai and not GEMINI_AVAILABLE:
            logger.warning("AI requested but google-generativeai not installed. Using basic reading.")

        logger.info(f"FileReaderAgent initialized. AI analysis is {'ENABLED' if self.gemini_model else 'DISABLED'}.")

    def _parse_github_url(self, url: str) -> tuple:
        """Parse GitHub URL to extract owner and repository name."""
        try:
            parsed = urlparse(url)
            path_parts = parsed.path.strip('/').split('/')
            if len(path_parts) >= 2:
                return path_parts[0], path_parts[1]
            else:
                raise ValueError("Invalid GitHub URL format")
        except Exception as e:
            logger.error(f"Failed to parse GitHub URL {url}: {e}")
            raise ValueError(f"Invalid GitHub URL: {url}")

    def _fetch_file_content(self, file_path: str) -> Optional[str]:
        """
        Fetch file content from GitHub using the raw content API.
        
        Args:
            file_path (str): Path to the file in the repository.
            
        Returns:
            str: File content or None if failed to fetch.
        """
        try:
            # GitHub raw content URL format
            raw_url = f"https://raw.githubusercontent.com/{self.owner}/{self.repo}/main/{file_path}"
            
            logger.info(f"Fetching file content from: {raw_url}")
            response = requests.get(raw_url, timeout=30)
            
            if response.status_code == 200:
                logger.info(f"Successfully fetched {file_path}")
                return response.text
            elif response.status_code == 404:
                # Try with 'master' branch if 'main' doesn't work
                master_url = f"https://raw.githubusercontent.com/{self.owner}/{self.repo}/master/{file_path}"
                logger.info(f"Trying master branch: {master_url}")
                response = requests.get(master_url, timeout=30)
                
                if response.status_code == 200:
                    logger.info(f"Successfully fetched {file_path} from master branch")
                    return response.text
                else:
                    logger.warning(f"File not found: {file_path} (tried both main and master branches)")
                    return None
            else:
                logger.error(f"Failed to fetch {file_path}: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching file {file_path}: {e}")
            return None

    def read_and_analyze_files(self, files_to_read: List[Dict]) -> Dict:
        """
        Read and analyze the specified files.
        
        Args:
            files_to_read (List[Dict]): List of file dictionaries with 'path' and 'reason'.
            
        Returns:
            Dict: Analysis results containing file contents and AI analysis if available.
        """
        logger.info(f"Starting to read and analyze {len(files_to_read)} files...")
        
        results = {
            "repository_url": self.repository_url,
            "files_analyzed": [],
            "summary": None,
            "errors": []
        }
        
        # Read all files first
        for file_info in files_to_read:
            file_path = file_info.get('path')
            reason = file_info.get('reason', 'No reason provided')
            
            logger.info(f"Reading file: {file_path}")
            content = self._fetch_file_content(file_path)
            
            file_result = {
                "path": file_path,
                "reason": reason,
                "content": content,
                "analysis": None,
                "error": None
            }
            
            if content is None:
                file_result["error"] = "Failed to fetch file content"
                results["errors"].append(f"Could not read {file_path}")
            elif self.gemini_model:
                # Analyze individual file with AI
                try:
                    file_result["analysis"] = self._analyze_file_with_ai(file_path, content, reason)
                except Exception as e:
                    logger.error(f"AI analysis failed for {file_path}: {e}")
                    file_result["error"] = f"AI analysis failed: {str(e)}"
            
            results["files_analyzed"].append(file_result)
        
        # Generate overall summary if AI is available
        if self.gemini_model:
            try:
                results["summary"] = self._generate_overall_summary(results["files_analyzed"])
            except Exception as e:
                logger.error(f"Failed to generate overall summary: {e}")
                results["errors"].append(f"Summary generation failed: {str(e)}")
        
        logger.info(f"Completed analysis of {len(files_to_read)} files with {len(results['errors'])} errors.")
        return results

    def _analyze_file_with_ai(self, file_path: str, content: str, reason: str) -> str:
        """
        Analyze a single file using Gemini AI.
        
        Args:
            file_path (str): Path to the file.
            content (str): File content.
            reason (str): Reason why this file was selected.
            
        Returns:
            str: AI analysis of the file.
        """
        prompt = f"""You are a DevOps engineer analyzing a critical file from a repository. 

File: {file_path}
Reason for selection: {reason}

File Content:
```
{content}
```

Please provide a concise analysis focusing on:
1. What this file does and its purpose
2. Key configuration details, dependencies, or setup requirements
3. Any important environment variables, ports, or external dependencies
4. Build/deployment instructions or requirements mentioned
5. Any potential issues or missing configurations

Keep your analysis practical and focused on what a developer needs to know to run this application."""

        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Gemini analysis failed for {file_path}: {e}")
            raise e

    def _generate_overall_summary(self, files_analyzed: List[Dict]) -> str:
        """
        Generate an overall summary of all analyzed files.
        
        Args:
            files_analyzed (List[Dict]): List of analyzed file results.
            
        Returns:
            str: Overall summary of the repository.
        """
        # Prepare content for summary
        file_summaries = []
        for file_info in files_analyzed:
            if file_info.get('content') and not file_info.get('error'):
                summary = f"File: {file_info['path']}\n"
                summary += f"Purpose: {file_info['reason']}\n"
                if file_info.get('analysis'):
                    summary += f"Analysis: {file_info['analysis']}\n"
                summary += "---\n"
                file_summaries.append(summary)
        
        if not file_summaries:
            return "No files were successfully analyzed."
        
        prompt = f"""Based on the analysis of these critical files from a repository, provide a comprehensive summary that includes:

1. **Application Type & Technology Stack**: What kind of application this is and what technologies it uses
2. **Architecture Overview**: How the application is structured (microservices, monolith, etc.)
3. **Setup Requirements**: What needs to be installed or configured to run this application
4. **Build & Run Instructions**: How to build and start the application
5. **Key Dependencies**: Important external services, databases, or APIs required
6. **Deployment Considerations**: Any containerization, environment variables, or deployment notes

File Analysis Results:
{chr(10).join(file_summaries)}

Provide a clear, actionable summary that would help a new developer understand and run this application."""

        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Failed to generate overall summary: {e}")
            raise e
