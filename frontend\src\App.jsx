import { useState, useEffect, useRef } from 'react';
import './App.css';

const CRAWLER_API_BASE_URL = 'http://localhost:8000';
const ANALYZER_API_BASE_URL = 'http://localhost:8001';
const READFILES_API_BASE_URL = 'http://localhost:8003';

function App() {
  const [repoUrl, setRepoUrl] = useState('');
  const [jobId, setJobId] = useState(null);
  const [jobStatus, setJobStatus] = useState(null);
  const [jobResult, setJobResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // New state for the analysis step
  const [analysisStatus, setAnalysisStatus] = useState(null); // e.g., 'identifying', 'completed', 'failed'
  const [analysisResult, setAnalysisResult] = useState(null);
  const [useAI, setUseAI] = useState(true); // Toggle for AI vs rule-based analysis

  // New state for file reading step
  const [fileReadingStatus, setFileReadingStatus] = useState(null); // e.g., 'reading', 'completed', 'failed'
  const [fileReadingResult, setFileReadingResult] = useState(null);
  
  const pollingInterval = useRef(null);

  // Helper function to render analysis results in a user-friendly way
  const renderAnalysisResult = (result) => {
    if (typeof result === 'string') {
      return <pre>{result}</pre>;
    }

    if (!result || !result.files_to_read) {
      return <pre>{JSON.stringify(result, null, 2)}</pre>;
    }

    const { files_to_read, analysis_method, ai_enabled } = result;

    return (
      <div className="analysis-results">
        <div className="analysis-metadata">
          <div className="analysis-method">
            <strong>Analysis Method:</strong> {analysis_method}
            {ai_enabled ? ' 🤖' : ' 📋'}
          </div>
          <div className="files-count">
            <strong>Files Selected:</strong> {files_to_read.length}
          </div>
        </div>

        <div className="files-list">
          <h3>Recommended Files to Analyze:</h3>
          {files_to_read.map((file, index) => (
            <div key={index} className="file-item">
              <div className="file-path">
                <code>{file.path}</code>
              </div>
              <div className="file-reason">
                {file.reason}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Helper function to render file reading results
  const renderFileReadingResult = (result) => {
    if (typeof result === 'string') {
      return <pre>{result}</pre>;
    }

    if (!result || !result.files_analyzed) {
      return <pre>{JSON.stringify(result, null, 2)}</pre>;
    }

    const { files_analyzed, summary, analysis_method, ai_enabled, errors } = result;

    return (
      <div className="file-reading-results">
        <div className="reading-metadata">
          <div className="analysis-method">
            <strong>Reading Method:</strong> {analysis_method}
            {ai_enabled ? ' 🤖' : ' 📄'}
          </div>
          <div className="files-count">
            <strong>Files Read:</strong> {files_analyzed.length}
          </div>
          {errors && errors.length > 0 && (
            <div className="errors-count">
              <strong>Errors:</strong> {errors.length}
            </div>
          )}
        </div>

        {summary && (
          <div className="overall-summary">
            <h3>📋 Repository Summary</h3>
            <div className="summary-content">
              <pre>{summary}</pre>
            </div>
          </div>
        )}

        <div className="files-content">
          <h3>📁 File Contents & Analysis</h3>
          {files_analyzed.map((file, index) => (
            <div key={index} className="file-content-item">
              <div className="file-header">
                <h4>
                  <code>{file.path}</code>
                  {file.error && <span className="error-badge">❌ Error</span>}
                </h4>
                <p className="file-reason">{file.reason}</p>
              </div>

              {file.error ? (
                <div className="file-error">
                  <strong>Error:</strong> {file.error}
                </div>
              ) : (
                <>
                  {file.analysis && (
                    <div className="file-analysis">
                      <h5>🤖 AI Analysis:</h5>
                      <div className="analysis-content">
                        <pre>{file.analysis}</pre>
                      </div>
                    </div>
                  )}

                  {file.content && (
                    <div className="file-content">
                      <h5>📄 File Content:</h5>
                      <div className="content-preview">
                        <pre>{file.content.length > 2000 ?
                          file.content.substring(0, 2000) + '\n\n... (content truncated)' :
                          file.content}
                        </pre>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </div>

        {errors && errors.length > 0 && (
          <div className="errors-section">
            <h3>⚠️ Errors</h3>
            <ul>
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  // Effect for polling the job status
  useEffect(() => {
    if (jobId && (jobStatus === 'running' || jobStatus === 'pending')) {
      pollingInterval.current = setInterval(checkCrawlStatus, 3000);
    }
    return () => clearInterval(pollingInterval.current);
  }, [jobId, jobStatus]);


  const handleCrawlRequest = async (e) => {
    e.preventDefault();
    if (isLoading) return;
    
    resetState();
    setIsLoading(true);
    setJobStatus('creating');

    try {
      const response = await fetch(`${CRAWLER_API_BASE_URL}/crawl`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: repoUrl }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to create job.');

      setJobId(data.job_id);
      setJobStatus('running');
      setJobResult(`Job created successfully! Polling for status... (ID: ${data.job_id})`);

    } catch (error) {
      setJobStatus('failed');
      setJobResult(`Creation Error: ${error.message}`);
      setIsLoading(false);
    }
  };

  const checkCrawlStatus = async () => {
    if (!jobId) return;

    try {
      const response = await fetch(`${CRAWLER_API_BASE_URL}/crawl/status/${jobId}`);
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to get status.');
      
      if (data.status === 'completed') {
        clearInterval(pollingInterval.current);
        setJobStatus(data.status);
        setJobResult(data.result);
        setIsLoading(false);
        // --- THIS IS THE NEW PART ---
        // Automatically trigger the analysis step
        handleAnalysisRequest(data); 
      } else if (data.status === 'failed') {
        clearInterval(pollingInterval.current);
        setJobStatus(data.status);
        setJobResult(`Job Failed: ${data.error}`);
        setIsLoading(false);
      } else {
        setJobStatus(data.status);
      }
    } catch (error) {
      clearInterval(pollingInterval.current);
      setJobStatus('failed');
      setJobResult(`Polling Error: ${error.message}`);
      setIsLoading(false);
    }
  };

  // --- NEW FUNCTION TO HANDLE ANALYSIS ---
  const handleAnalysisRequest = async (crawlData) => {
    setAnalysisStatus('identifying');
    setAnalysisResult(`Identifying critical files using ${useAI ? 'AI-powered' : 'rule-based'} analysis...`);

    try {
      const response = await fetch(`${ANALYZER_API_BASE_URL}/identify-files?use_ai=${useAI}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(crawlData) // Send the full crawl result
      });

      const analysisData = await response.json();
      if (!response.ok) throw new Error(analysisData.error || 'File identification failed.');

      setAnalysisStatus('completed');
      setAnalysisResult(analysisData);

      // --- AUTOMATICALLY TRIGGER FILE READING AFTER ANALYSIS COMPLETES ---
      if (analysisData.files_to_read && analysisData.files_to_read.length > 0) {
        await handleFileReadingRequest(analysisData, crawlData.repository_url);
      }

    } catch (error) {
      setAnalysisStatus('failed');
      setAnalysisResult(`Analysis Error: ${error.message}`);
    }
  };

  // --- NEW FUNCTION TO HANDLE FILE READING ---
  const handleFileReadingRequest = async (analysisData, repositoryUrl) => {
    setFileReadingStatus('reading');
    setFileReadingResult(`Reading and analyzing ${analysisData.files_to_read.length} files using ${useAI ? 'AI-powered' : 'basic'} analysis...`);

    try {
      const response = await fetch(`${READFILES_API_BASE_URL}/read-files?use_ai=${useAI}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repository_url: repositoryUrl,
          files_to_read: analysisData.files_to_read
        })
      });

      const fileData = await response.json();
      if (!response.ok) throw new Error(fileData.error || 'File reading failed.');

      setFileReadingStatus('completed');
      setFileReadingResult(fileData);

    } catch (error) {
      setFileReadingStatus('failed');
      setFileReadingResult(`File Reading Error: ${error.message}`);
    }
  };

  const handleCloseJob = async () => {
    if (!jobId) return;
    setIsLoading(true);
    clearInterval(pollingInterval.current);

    try {
      const response = await fetch(`${CRAWLER_API_BASE_URL}/crawl/jobs/${jobId}`, { method: 'DELETE' });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to close job.');
      resetState();
      alert(data.message);
    } catch (error) {
      alert(`Close Error: ${error.message}`);
      setIsLoading(false);
    }
  };
  
  const resetState = () => {
    clearInterval(pollingInterval.current);
    setJobId(null);
    setJobStatus(null);
    setJobResult(null);
    setIsLoading(false);
    // Reset analysis state as well
    setAnalysisStatus(null);
    setAnalysisResult(null);
    // Reset file reading state as well
    setFileReadingStatus(null);
    setFileReadingResult(null);
    // Keep AI setting - don't reset it
  };

  return (
    <main className="container">
      <h1>GitCrawl and Analyze</h1>
      <form onSubmit={handleCrawlRequest}>
        <input
          type="url"
          value={repoUrl}
          onChange={(e) => setRepoUrl(e.target.value)}
          placeholder="Enter GitHub Repository URL"
          required
          disabled={isLoading}
        />
        <button type="submit" disabled={isLoading}>
          {isLoading ? 'Processing...' : 'Crawl'}
        </button>
      </form>

      {/* AI Analysis Toggle */}
      <div className="analysis-settings">
        <label className="toggle-container">
          <input
            type="checkbox"
            checked={useAI}
            onChange={(e) => setUseAI(e.target.checked)}
            disabled={isLoading}
          />
          <span className="toggle-label">
            Use AI-powered file analysis {useAI ? '🤖' : '📋'}
          </span>
        </label>
        <p className="toggle-description">
          {useAI
            ? 'AI will intelligently select the most important files to analyze based on project structure and content.'
            : 'Use simple rule-based pattern matching to identify common configuration files.'
          }
        </p>
      </div>

      {jobStatus && (
        <div className={`status-box ${jobStatus}`}>
          <div className="status-header">
            <strong>Crawl Status: {jobStatus.toUpperCase()}</strong>
            {jobId && (
              <button onClick={handleCloseJob} disabled={isLoading} className="close-btn">
                Close Job
              </button>
            )}
          </div>
          <div className="result-area">
            <pre>
              {typeof jobResult === 'object' && jobResult !== null
                ? JSON.stringify(jobResult, null, 2)
                : jobResult}
            </pre>
          </div>
        </div>
      )}
      
      {/* --- ENHANCED UI FOR ANALYSIS RESULT --- */}
      {analysisStatus && (
        <div className={`analysis-box ${analysisStatus}`}>
          <div className="status-header">
            <strong>File Analysis: {analysisStatus.toUpperCase()}</strong>
          </div>
          <div className="result-area">
            {renderAnalysisResult(analysisResult)}
          </div>
        </div>
      )}

      {/* --- NEW UI FOR FILE READING RESULTS --- */}
      {fileReadingStatus && (
        <div className={`file-reading-box ${fileReadingStatus}`}>
          <div className="status-header">
            <strong>File Reading & Analysis: {fileReadingStatus.toUpperCase()}</strong>
          </div>
          <div className="result-area">
            {renderFileReadingResult(fileReadingResult)}
          </div>
        </div>
      )}
    </main>
  );
}

export default App;