# ReadFiles Microservice

A microservice that reads and analyzes important files from GitHub repositories using the Gemini AI model.

## Overview

The ReadFiles microservice receives a list of critical files (typically from the GitAnalyser service) and:
1. Fetches the actual file contents from GitHub repositories
2. Uses Gemini AI to analyze each file's purpose and configuration
3. Generates an overall summary of the repository's setup and requirements

## Features

- **AI-Powered Analysis**: Uses Gemini 2.5 Flash to intelligently analyze file contents
- **GitHub Integration**: Fetches files directly from GitHub repositories (both main and master branches)
- **Comprehensive Analysis**: Provides both individual file analysis and overall repository summary
- **Fallback Support**: Works without AI for basic file reading
- **Error Handling**: Robust error handling with detailed logging

## API Endpoints

### POST /read-files

Reads and analyzes files from a GitHub repository.

**Request Body:**
```json
{
  "repository_url": "https://github.com/owner/repo",
  "files_to_read": [
    {
      "path": "README.md",
      "reason": "Main documentation file"
    },
    {
      "path": "package.json",
      "reason": "NPM dependencies and scripts"
    }
  ]
}
```

**Query Parameters:**
- `use_ai` (optional): Set to "false" to disable AI analysis (default: "true")

**Response:**
```json
{
  "repository_url": "https://github.com/owner/repo",
  "analysis_method": "AI-Enhanced",
  "ai_enabled": true,
  "files_analyzed": [
    {
      "path": "README.md",
      "reason": "Main documentation file",
      "content": "# Project Title\n...",
      "analysis": "This README file provides...",
      "error": null
    }
  ],
  "summary": "This is a Node.js application that...",
  "errors": []
}
```

### GET /health

Health check endpoint.

**Response:**
```json
{
  "service": "ReadFiles",
  "status": "healthy"
}
```

## Environment Variables

- `GEMINI_API_KEY`: Required for AI analysis functionality
- `PORT`: Service port (default: 8003)
- `FLASK_ENV`: Set to "development" for debug mode

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
export GEMINI_API_KEY="your-gemini-api-key"
export PORT=8003
```

3. Run the service:
```bash
python app.py
```

## Docker Support

You can also run this service in a Docker container. Make sure to pass the required environment variables.

## Integration with GitAnalyser

This service is designed to work with the GitAnalyser microservice:

1. GitAnalyser identifies critical files in a repository
2. ReadFiles receives the list and fetches/analyzes the actual file contents
3. The combined result provides both file structure analysis and content analysis

## Error Handling

The service handles various error scenarios:
- Invalid GitHub URLs
- Missing files (tries both main and master branches)
- Network timeouts
- AI analysis failures (falls back to basic file reading)
- Invalid request formats

All errors are logged with detailed information and returned in the response.
